export const DUMMY_DATA = [
  {
    product_name: 'Product A',
    product_sku: 'SKU-001',
    msda_files: [
      {
        name: 'MSDS_ProductA.pdf',
        url: 'https://example.com/files/msda1.pdf',
        file_size: 1024,
      },
    ],
    pmta_files: [
      {
        name: 'PMTA_ProductA.pdf',
        url: 'https://example.com/files/pmta1.pdf',
        file_size: 2048,
      },
    ],
  },
  {
    product_name: 'Product B',
    product_sku: 'SKU-002',
    msda_files: [],
    pmta_files: [
      {
        name: 'PMTA_ProductB.pdf',
        url: 'https://example.com/files/pmta2.pdf',
        file_size: 1536,
      },
    ],
  },
  {
    product_name: 'Product C',
    product_sku: 'SKU-003',
    msda_files: [
      {
        name: 'MSDS_ProductC.pdf',
        url: 'https://example.com/files/msda3.pdf',
        file_size: 3072,
      },
    ],
    pmta_files: [],
  },
  {
    product_name: 'Product D',
    product_sku: 'SKU-004',
    msda_files: [],
    pmta_files: [],
  },
]

export const parseProductFilesReportData = (data: any) => {
  return data?.map((item: any) => ({
    productName: item.name || '-',
    customUrl: item?.custom_url?.url || '',
    productId: item.id || '',
    sku: item.sku || '-',
    classification:
      typeof item?.classification !== 'string'
        ? item?.classification?.join(', ')
        : item?.classification || '-',
    msdaFiles: item?.attachments?.msda?.length,
    pmtaFiles: item?.attachments?.pmta?.length,
    mediaKit: item?.attachments?.mediakit?.length,
    mediaKitUrl: item?.attachments?.mediakit_url || '',
  }))
}
