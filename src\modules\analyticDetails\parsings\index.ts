import {isEmpty} from 'lodash'

export function dataToReplenishmentReport(data: any[]) {
  if (!data || !Array.isArray(data)) return []

  return data.map((item: any) => ({
    productTitle: item['product_title'] || '-',
    parentSku: item['parent_sku'] || '-',
    variantSku: item['sku'] || null,
    quantityIncoming: item['quantity_incoming'] || '-',
    quantityAvailable: item['quantity_available'] || '-',
    reservedQuantity: item['reserved_quantity'] || '-',
    totalZeroQuantity: item['is_parent']
      ? item['total_zero_quantity']
      : item['zero_quantity'] || '',
    occurrenceRange: item['is_parent']
      ? item['occurrence_range']
      : item['total_out_of_stock_days'] || '',
    lastOutOfStockDate: item['last_out_of_stock_date'] || item['out_of_stock_date'] || '',
    lastReceivedDate: item['last_received_date'],
    lastReceivedQuantity: item['restocked_inventory_level'] || '-',
    rtv: item['is_parent'] ? item['total_rtv_quantity'] : item['rtv_quantity'] || '',
    month_1: item['month_1'] || '-',
    month_2: item['month_2'] || '-',
    month_3: item['month_3'] || '-',
    month_4: item['month_4'] || '-',
    month_5: item['month_5'] || '-',
    month_6: item['month_6'] || '-',
    month_7: item['month_7'] || '-',
    isParent: item['is_parent'] || false,
  }))
}

export const parseOutOfStockOccurrenceData = (data: any[]): any[] => {
  if (isEmpty(data)) return []

  return data.map((item) => ({
    childSKU: item.sku || '-',
    outOfStockDate: item.out_of_stock_start_date || null,
    restockDate: item.out_of_stock_end_date || null,
    restockQuantity: item.inventory_level || '-',
  }))
}
