import {isEmpty} from '../../../utils/common'

// Product Return Listing Parsing Functions
export const parseProductReturnListingData = (data: any[]) => {
  if (isEmpty(data)) return []

  return data.map((item) => ({
    productName: item?.parent_product_title || '-',
    productSku: item?.parent_sku || '-',
    returnCount: item?.total_product_returned_quantity,

    hasChildren: item?.child_data && Array.isArray(item?.child_data) && item?.child_data.length > 0,
    variants:
      item?.child_data && Array.isArray(item?.child_data)
        ? item.child_data.map((variant: any) => ({
            productName: item?.parent_product_title || '-',
            variantName: variant?.variant_product_title || '-',
            parentSku: item?.parent_sku || null,
            variantSku: variant?.variant_sku || '-',
            returnCount: variant?.total_returned_quantity,
          }))
        : undefined,
  }))
}
