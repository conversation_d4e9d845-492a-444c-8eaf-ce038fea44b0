import {useState} from 'react'
import {formatReturnCount, getExpandIcon} from '../../utils'
import ProductReturnDetailsModal from '../models/ProductReturnDetailsModal'
import ProductVariantsReturnTable from './ProductVariantsReturnTable'

export const ProductReturnTableRow = ({row, columns}: any) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showProductReturnDetailsModal, setShowProductReturnDetailsModal] = useState(false)

  return (
    <>
      <tr>
        <td>
          {row.hasChildren && (
            <i
              className={getExpandIcon(isExpanded, row.hasChildren)}
              onClick={() => setIsExpanded(!isExpanded)}
            ></i>
          )}
        </td>
        <td>{row.productName}</td>
        <td>{row.productSku}</td>
        <td>
          {row?.returnCount > 0 ? (
            <span
              className='text-hover-primary fw-bold cursor-pointer text-decoration-underline'
              onClick={() => setShowProductReturnDetailsModal(true)}
            >
              {formatReturnCount(row.returnCount)}
            </span>
          ) : (
            row?.returnCount
          )}
        </td>
      </tr>

      {isExpanded && row.hasChildren && (
        <td colSpan={columns.length || 4}>
          <ProductVariantsReturnTable variants={row.variants} />
        </td>
      )}

      {showProductReturnDetailsModal && (
        <ProductReturnDetailsModal
          show={showProductReturnDetailsModal}
          onClose={() => setShowProductReturnDetailsModal(false)}
          parentSku={row.productSku}
          title={row.productName}
          isParent={true}
        />
      )}
    </>
  )
}

export default ProductReturnTableRow
