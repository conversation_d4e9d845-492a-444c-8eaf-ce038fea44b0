import {useContext} from 'react'
import {Link} from 'react-router-dom'
import {ConsignmentsListingContext} from '../../context'
import usePermission from '../../../../hook/usePermission'

function ConsignmentsTableRow({row, handleCheckboxChange, checkedIds}: any) {
  const {changeVisibility, lastOperationData}: any = useContext(ConsignmentsListingContext)
  const {hasPermission} = usePermission()

  return (
    <tr>
      <td>
        <div className='form-check form-check-sm form-check-custom form-check-solid'>
          {hasPermission('analytics_consignments', 'write') ? (
            <input
              className='form-check-input widget-9-check'
              type='checkbox'
              checked={checkedIds.includes(row['id'])}
              onChange={() => handleCheckboxChange(row['id'])}
            />
          ) : (
            <input className='form-check-input widget-9-check' type='checkbox' disabled={true} />
          )}
        </div>
      </td>
      <td>
        <div className='align-items-center'>
          <Link
            to={`/analytics/consignments/all/${row['id']}/${row['variant_sku']}`}
            className='text-dark text-hover-primary fs-6'
            state={{productName: row['product_name']}}
            onClick={() => {
              localStorage.setItem('detailScreenProductName', row['product_name'])
            }}
          >
            {row['product_name']}
          </Link>
        </div>
      </td>
      <td>
        <div className='align-items-center'>{row['variant_sku']}</div>
      </td>
      <td>
        <div className='align-items-center text-center'>{row['inventory_level']}</div>
      </td>
      <td>
        <div className='align-items-center text-center'>{row['availableQty']}</div>
      </td>
      <td className='text-start'>
        {hasPermission('analytics_consignments', 'write') ? (
          <div className='d-flex justify-content-center flex-shrink-0'>
            {(row['id'] in lastOperationData && !lastOperationData[row['id']]) ||
            !row['isVisible'] ? (
              <i
                className='text-danger bi-eye-slash fs-2 cursor-pointer'
                onClick={() => changeVisibility([row['id']], '1')}
              />
            ) : (
              <i
                className='text-success bi bi-eye fs-2 cursor-pointer'
                onClick={() => changeVisibility([row['id']], '')}
              />
            )}
          </div>
        ) : (
          <div className='d-flex justify-content-center flex-shrink-0'>
            {(row['id'] in lastOperationData && !lastOperationData[row['id']]) ||
            !row['isVisible'] ? (
              <i className='text-danger bi-eye-slash fs-2' />
            ) : (
              <i className='text-success bi bi-eye fs-2' />
            )}
          </div>
        )}
      </td>
    </tr>
  )
}

export default ConsignmentsTableRow
