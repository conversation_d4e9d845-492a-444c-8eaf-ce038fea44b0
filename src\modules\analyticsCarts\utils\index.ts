import {formatPrice} from '../../../utils/common'
import {formatDate} from '../../../utils/date'

export const parseCartsTableRowData = (row: any) => {
  return {
    cart_id: row?.cart_id || '-',
    cart_value: (row?.cart_value && formatPrice(row?.cart_value, true)) || 0,
    channel: row?.channel || '-',
    customer_id: row?.customer_id || '-',
    customer_name: row?.customer_name || '-',
    customerFirstNameLetter: row?.customer_name
      ? row['customer_name'].charAt(0).toUpperCase()
      : row?.firstName
      ? row.firstName.charAt(0).toUpperCase()
      : '',
    email: row?.email || '-',
    customer_rep: row?.customer_rep || '-',
    customer_rep_id: row?.customer_rep_id || '-',
    is_live: row?.is_live ? 'true' : 'false',
    line_items: row?.line_items || 0,
    updated_at: row?.updated_at ? formatDate(row.updated_at, false) : '-',
  }
}

export const parseCartsInventoryTableRowData = (row: any) => {
  const parentName = row?.product_name || '-'
  const variantName = row?.variant_options || 'Parent Product'

  return {
    parent_product_name: parentName,
    variant_name: variantName,
    product_name: parentName, // Keeping this for table display
    productName: variantName ? `${parentName} - ${variantName}` : parentName, // This will be used for popup title
    sku: row?.variant_sku || '-',
    inventory: row?.inventory_level ?? '-',
    cart: row?.total_cart_quantity ?? 0,
    product_id: row?.product_id || '-',
  }
}

export const cartsTableColumns = [
  {
    key: 'customer_name',
    label: 'Customer name',
    isSorted: false,
    headerStyle: 'min-w-200px',
  },
  {
    key: 'customer_rep',
    label: 'Customer rep',
    isSorted: false,
    headerStyle: 'min-w-200px',
  },
  {
    key: 'channel',
    label: 'Channel',
    isSorted: true,
    headerStyle: 'min-w-100px',
  },
  {
    key: 'line_items',
    label: 'Line Items',
    isSorted: true,
    headerStyle: 'min-w-120px',
  },
  {
    key: 'cart_value',
    label: 'Cart Value',
    isSorted: true,
    headerStyle: 'min-w-150px',
  },
  {
    key: 'updated_at',
    label: 'Updated At',
    isSorted: true,
    headerStyle: 'min-w-150px',
  },
  {
    key: 'action',
    label: 'Action',
    isSorted: false,
    headerStyle: 'w-80px',
  },
]

export const cartsInventoryTableColumns = [
  {
    key: 'product_name',
    label: 'Product Name',
    isSorted: true,
    headerStyle: 'min-w-400px',
  },
  {
    key: 'variant_sku',
    label: 'SKU',
    isSorted: true,
    headerStyle: 'w-200px',
  },
  {
    key: 'inventory_level',
    label: 'Inventory',
    isSorted: true,
    headerStyle: 'w-120px',
  },
  {
    key: 'total_cart_quantity',
    label: 'Cart QTY',
    isSorted: true,
    headerStyle: 'w-150px',
  },
]
