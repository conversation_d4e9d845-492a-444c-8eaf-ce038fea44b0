{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "allpepper-memory-bank": {"command": "cmd /c npx", "args": ["-y", "@allpepper/memory-bank-mcp@latest"], "env": {"MEMORY_BANK_ROOT": "P:\\Local_MCPs\\memory-bank"}, "transport": "stdio"}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest", "--vision"]}, "mcp-compass": {"command": "npx", "args": ["-y", "@liuyoshio/mcp-compass"]}}}