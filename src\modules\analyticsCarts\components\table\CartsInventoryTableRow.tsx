import {useState} from 'react'
import {parseCartsInventoryTableRowData} from '../../utils'
import CartDetailsPopup from '../modal/CartDetailsPopup'

const CartsInventoryTableRow = ({row: rowData}: any) => {
  const row = parseCartsInventoryTableRowData(rowData)
  const [showPopup, setShowPopup] = useState(false)

  const togglePopup = () => setShowPopup(!showPopup)
  const isLowInventory = row?.inventory < row?.cart

  return (
    <>
      <tr>
        {/* Product Name with Variant */}
        <td>
          {row.parent_product_name}
          <br />
          <strong>{row.variant_name}</strong>
        </td>

        {/* SKU */}
        <td>{row.sku}</td>

        {/* Inventory */}
        <td className={isLowInventory ? 'text-danger' : ''}>{row.inventory}</td>

        {/* Cart Quantity with clickable popup */}
        <td>
          {row.cart ? (
            <button className='btn btn-sm text-decoration-underline p-0' onClick={togglePopup}>
              {row.cart}
            </button>
          ) : (
            row.cart
          )}
        </td>
      </tr>

      {/* Cart Details Popup */}
      {showPopup && (
        <CartDetailsPopup
          show={showPopup}
          onClose={togglePopup}
          productName={row.productName}
          productId={row.sku}
        />
      )}
    </>
  )
}

export default CartsInventoryTableRow
