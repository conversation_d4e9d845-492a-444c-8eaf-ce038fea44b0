export function dataToConsignments(data: any) {
  const products: any = []
  if (!data) {
    return []
  }

  data?.forEach((element: any) =>
    products.push({
      id: element['product_id'],
      product_name: element['product_name'],
      variant_sku: element['variant_sku'] || '-',
      inventory_level: element['inventory_level'],
      availableQty: element['quantity'],
      isVisible: element['is_visible'],
    })
  )

  return products
}
