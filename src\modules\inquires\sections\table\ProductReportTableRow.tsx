import {Link} from 'react-router-dom'
import {useAppSelector} from '../../../../redux/useTypedSelector'

const ProductReportTableRow = ({row}: {row: any}) => {
  const {user} = useAppSelector((state) => state.auth)

  return (
    <tr>
      <td>{row.productName}</td>
      <td>{row.sku}</td>
      <td className='text-center bg-light'>
        {row.total > 0 && user?.is_owner ? (
          <Link
            to={{
              pathname: '/products/inquiries/all-inquiries',
              search: `?product=${row.sku}`,
            }}
            target='_blank'
            className='cursor-pointer text-dark text-decoration-underline text-hover-primary'
          >
            {row.total}
          </Link>
        ) : (
          row.total
        )}
      </td>
      <td className='text-center'>
        {row.open > 0 && user?.is_owner  ? (
          <Link
            to={{
              pathname: '/products/inquiries/all-inquiries',
              search: `?product=${row.sku}&status=Open`,
            }}
            target='_blank'
            className='cursor-pointer text-dark text-decoration-underline text-hover-primary'
          >
            {row.open}
          </Link>
        ) : (
          row.open
        )}
      </td>
      <td className='text-center'>
        {row.inProgress > 0  && user?.is_owner ? (
          <Link
            to={{
              pathname: '/products/inquiries/all-inquiries',
              search: `?product=${row.sku}&status=In Progress`,
            }}
            target='_blank'
            className='cursor-pointer text-dark text-decoration-underline text-hover-primary'
          >
            {row.inProgress}
          </Link>
        ) : (
          row.inProgress
        )}
      </td>
      <td className='text-center'>
        {row.pending > 0 && user?.is_owner  ? (
          <Link
            to={{
              pathname: '/products/inquiries/all-inquiries',
              search: `?product=${row.sku}&status=Pending`,
            }}
            target='_blank'
            className='cursor-pointer text-dark text-decoration-underline text-hover-primary'
          >
            {row.pending}
          </Link>
        ) : (
          row.pending
        )}
      </td>
      <td className='text-center'>
        {row.done > 0  && user?.is_owner ? (
          <Link
            to={{
              pathname: '/products/inquiries/all-inquiries',
              search: `?product=${row.sku}&status=Done`,
            }}
            target='_blank'
            className='cursor-pointer text-dark text-decoration-underline text-hover-primary'
          >
            {row.done}
          </Link>
        ) : (
          row.done
        )}
      </td>
      <td className='text-center'>
        {row.archived > 0 && user?.is_owner  ? (
          <Link
            to={{
              pathname: '/products/inquiries/archived',
              search: `?product=${row.sku}`,
            }}
            target='_blank'
            className='cursor-pointer text-dark text-decoration-underline text-hover-primary'
          >
            {row.archived}
          </Link>
        ) : (
          row.archived
        )}
      </td>
    </tr>
  )
}

export default ProductReportTableRow
