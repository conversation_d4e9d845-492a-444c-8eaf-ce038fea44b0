export const parseCartLineItemDetails = (row: any) => {
  return {
    id: row?.bc_item_id,
    sku: row?.sku || '-',
    productName: row?.product_name || '-',
    quantity: row?.quantity || null,
    price: row?.price || 0,
    totalCost: row?.total_cost || 0,
    isExisting: row?.is_existing ?? true,
    minQtyLimit: row?.min_purchase_quantity || 1,
    maxQtyLimit: row?.max_purchase_quantity || +Infinity,
    availableQuantity: row?.available_quantity || 0,
    isDeleted: row?.isDeleted || false,
  }
}
