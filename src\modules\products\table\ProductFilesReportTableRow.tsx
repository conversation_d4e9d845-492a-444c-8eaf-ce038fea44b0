import {useContext, useState} from 'react'
import {Link} from 'react-router-dom'
import {ProductFilesReportContext} from '../contexts'
import useToastify from '../../../hook/useToastify'

const ProductFilesReportTableRow = ({row}: any) => {
  const {onDownload} = useContext(ProductFilesReportContext)
  const [downloadingType, setDownloadingType] = useState<string | null>(null)
  const {toastMessage} = useToastify()

  const handleDownload = async (type: string, productId: any, count: any) => {
    setDownloadingType(type)
    await onDownload(type, productId, count)
    setDownloadingType(null)
  }

  const handleCopyUrl = async (url: string) => {
    await navigator.clipboard.writeText(url)
    toastMessage('success', `Media Kit URL copied to your clipboard`)
  }

  return (
    <tr>
      <td>
        <Link
          to={`/products/all-products/${row.productId}${row?.customUrl}`}
          target='_blank'
          className='text-dark text-hover-primary'
        >
          {row.sku}
        </Link>
      </td>
      <td>
        <Link
          to={`/products/all-products/${row.productId}${row?.customUrl}`}
          target='_blank'
          className='text-dark text-hover-primary fw-semibold'
        >
          {row.productName}
        </Link>
      </td>
      <td>{row.classification}</td>
      <td>
        {row.msdaFiles ? (
          downloadingType === 'msda' ? (
            <div className='w-30px h-30px'>
              <span className='spinner-border spinner-border-sm ms-2' role='status' />
            </div>
          ) : (
            <button
              className='btn btn-sm btn-icon btn-light-primary w-30px h-30px'
              onClick={() => handleDownload('msda', row.productId, row.msdaFiles)}
              title='Download MSDS Files'
              disabled={downloadingType !== null}
            >
              <i className='bi bi-cloud-arrow-down fs-3'></i>
            </button>
          )
        ) : (
          '-'
        )}
      </td>
      <td>
        {row.pmtaFiles ? (
          downloadingType === 'pmta' ? (
            <div className='w-30px h-30px'>
              <span className='spinner-border spinner-border-sm ms-2' role='status' />
            </div>
          ) : (
            <button
              className='btn btn-sm btn-icon btn-light-primary w-30px h-30px'
              onClick={() => handleDownload('pmta', row.productId, row.pmtaFiles)}
              title='Download PMTA Files'
              disabled={downloadingType !== null}
            >
              <i className='bi bi-cloud-arrow-down fs-3'></i>
            </button>
          )
        ) : (
          '-'
        )}
      </td>
      <td>
        {!row.mediaKit ? (
          '-'
        ) : (
          <div className='d-flex gap-5'>
            {row.mediaKit ? (
              downloadingType === 'mediakit' ? (
                <div className='w-30px h-30px'>
                  <span className='spinner-border spinner-border-sm ms-2' role='status' />
                </div>
              ) : (
                <button
                  className='btn btn-sm btn-icon btn-light-primary w-30px h-30px'
                  onClick={() => handleDownload('mediakit', row.productId, row.mediaKit)}
                  title='Download Media Kit'
                  disabled={downloadingType !== null}
                >
                  <i className='bi bi-cloud-arrow-down fs-3'></i>
                </button>
              )
            ) : null}
          </div>
        )}
      </td>
      <td>
        {row.mediaKitUrl ? (
          <button
            className='btn btn-sm btn-icon btn-light-primary w-30px h-30px'
            onClick={() => handleCopyUrl(row.mediaKitUrl)}
            title='Copy Media Kit URL'
            disabled={downloadingType !== null}
          >
            <i className='bi bi-clipboard-plus fs-3'></i>
          </button>
        ) : (
          '-'
        )}
      </td>
    </tr>
  )
}

export default ProductFilesReportTableRow
