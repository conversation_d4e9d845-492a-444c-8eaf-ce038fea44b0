import {isEmpty} from '../../../utils/common'
import {IInStockNotifyProduct, IInquiryDetail} from '../interfaces'

export const parseInStockNotifyData = (data: any[]): IInStockNotifyProduct[] => {
  if (isEmpty(data)) return []

  return data.map((item) => ({
    productId: item.product_id?.toString() || null,
    productName: item.product_name || '-',
    productSku: item.product_sku || '-',
    inquiryCount: item.inquiry || 0,
    hasChildren: item.variants && Array.isArray(item.variants) && item.variants.length > 0,
    lastCreatedAt: item.last_created_at || '-',
    variants: item.variants && Array.isArray(item.variants)
      ? item.variants.map((variant: any) => ({
          variantId: variant.variant_id?.toString() || null,
          variantName: variant.variant_name || '-',
          variantSku: variant.variant_sku || '-',
          productName: item.product_name || '-',
          inquiryCount: variant.inquiry || 0,
          hasChildren: false,
          productId: item.product_id?.toString() || null,
          lastCreatedAt: variant.last_created_at || '-',
        }))
      : undefined,
  }))
}

export const parseInquiryData = (data: any[]): IInquiryDetail[] => {
  if (isEmpty(data)) return []

  return data.map((item, index) => ({
    id: `${item.customer_email}-${item.created_at}-${index}`,
    customerName: item.customer_name || '',
    email: item.customer_email || '-',
    inquiryDate: item.created_at || '',
    sku: item.sku || '-',
    productName: item.product_name || '-',
    customerGroup: item.customer_group || '-',
    salesRep: item.sales_rep || '-',
    notifyEmail: item.notify_email || '-',
    variantId: item.variant_id?.toString() || null,
  }))
}
