/* eslint-disable react-hooks/exhaustive-deps */
import {useContext, useEffect} from 'react'
import {formatPrice} from '../../../../utils/common'
import {AnalyticsCartDetailsContext} from '../../contexts'
import CartItemsDetailsTableOverlay from '../overlays/CartItemsDetailsTableOverlay'
import {InputNumber} from '../../../../components/InputNumber'
import {cartQuantityValidation} from '../../validations'
import {getCartItemTotal} from '../../utils'

const CartItemsDetailsTableRow = ({row, hasWritePermission, setValue, index}: any) => {
  const {isEdit, form} = useContext(AnalyticsCartDetailsContext)
  const {
    register,
    watch,
    formState: {errors, dirtyFields},
  } = form

  useEffect(() => {
    if (!row?.isExisting) {
      setValue(`products.${row.sku}.quantity`, row?.quantity, {shouldDirty: true})
    }
  }, [index])

  return (
    <tr className={!row?.isExisting ? 'bg-light-success' : row?.isDeleted ? 'bg-light-danger' : ''}>
      <td>{row['sku']}</td>
      <td>{row['productName']}</td>
      <td>
        <div className='text-center'>{row['availableQuantity']}</div>
      </td>
      <td>
        {isEdit ? (
          <InputNumber
            name='Qty'
            register={register(
              `products.${row.sku}.quantity`,
              cartQuantityValidation(row.sku, dirtyFields, row)
            )}
            id='product-qty'
            className='d-flex flex-column justify-content-center align-items-center'
            inputClass={`text-center w-100px ${
              errors?.products?.[row.sku]?.quantity
                ? 'bg-light-danger'
                : dirtyFields?.products?.[row.sku]?.quantity
                ? 'bg-light-success'
                : ''
            }`}
            errorClass='text-center text-nowrap'
            error={errors?.products?.[row.sku]?.quantity}
          />
        ) : (
          <div className='text-center'>{row['quantity']}</div>
        )}
      </td>
      <td>
        <div className='text-end'>{formatPrice(row['price'], true)}</div>
      </td>
      <td>
        <div className='text-end'>{getCartItemTotal(watch(), row)}</div>
      </td>
      {hasWritePermission ? (
        <td>
          <CartItemsDetailsTableOverlay row={row} />
        </td>
      ) : null}
    </tr>
  )
}

export default CartItemsDetailsTableRow
