import {formatPrice, formatToIndianNumber} from '../../../utils/common'

export const parseSupplierReportRow = (row: any) => {
  return {
    id: row.id,
    supplierName: row.supplier_name || '-',
    orderCount: row.total_orders || 0,
    totalSale: formatPrice(row.total_sales, false) || 0,
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    totalProfitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parseProductWiseReportRow = (row: any) => {
  return {
    id: row.id,
    productId: row.product_id || '-',
    productName: row.product_name || '-',
    sku: row.product_sku || '-',
    orderCount: row.total_orders || 0,
    classifications: row.classification || '-',
    suppliers: row.primary_supplier || '-',
    totalSale: formatPrice(row.total_sales, false) || 0,
    averageCostPerPiece: formatPrice(row.average_cost_per_piece, false) || 0,
    averageSellingPricePerPiece: formatPrice(row.average_selling_price_per_piece, false) || 0,
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    totalProfitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parseClassificationReportRow = (row: any) => {
  return {
    id: row.id,
    classificationName: row.classification || '-',
    orderCount: row.total_orders || 0,
    totalSale: formatPrice(row.total_sales, false) || 0,
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    totalProfitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parseCustomerRepReportRow = (row: any) => {
  return {
    id: row.id,
    salesRep: row.customer_rep || '-',
    salesRepEmail: row.rep_email || '-',
    salesRepType: row.rep_type || '-',
    orderCount: row.total_orders || 0,
    profitableOrders: row.profitable_orders || 0,
    nonProfitableOrders: row.non_profitable_orders || 0,
    totalSale: formatPrice(row.total_sales, false) || 0,
    contributionSalePercentage: `${row.contributed_revenue_percentage}%` || '0%',
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    totalProfitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parseCustomerGroupReportRow = (row: any) => {
  return {
    id: row.id,
    customerGroupName: row.customer_group_name || '-',
    customerGroupId: row.customer_group_id || '-',
    orderCount: row.total_orders || 0,
    totalSale: formatPrice(row.total_revenue, false) || 0,
    contributionSalePercentage: `${row.contributed_revenue_percentage}%` || '0%',
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    totalProfitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parseCustomerTypeReportRow = (row: any) => {
  return {
    id: row.id,
    customerTypeName: row.customer_type || '-',
    orderCount: row.total_orders || 0,
    totalSale: formatPrice(row.total_sales, false) || 0,
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    totalProfitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parsePurchaserReportRow = (row: any) => {
  return {
    id: row.id,
    purchaserName: row.purchaser_name || '-',
    orderCount: formatToIndianNumber(row?.total_orders) || '-',
    totalSale: formatPrice(row.total_sales, false) || 0,
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    profitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parsePurchaserByBrandReportRow = (row: any) => {
  return {
    id: row.id,
    purchaserName: row.purchaser_name || '-',
    purchaserEmail: row.purchaser_email || '-',
    orderCount: formatToIndianNumber(row?.total_orders) || '-',
    totalSale: formatPrice(row.total_sales, false) || 0,
    totalCost: formatPrice(row.total_cost, false) || 0,
    totalProfit: formatPrice(row.total_profit, false) || 0,
    profitPercentage: `${row.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${row.contributed_profit_percentage}%` || '0%',
  }
}

export const parseCustomerGroupRow = (data: any) => {
  return {
    customerGroup: data.customer_group_name || '-',
    customerGroupId: data.customer_group_id || '-',
    orderCount: data.total_orders || 0,
    totalCost: formatPrice(data.total_cost, true),
    revenue: formatPrice(data.total_revenue, true),
    contributionSalePercentage: `${data.contributed_revenue_percentage}%` || '0%',
    totalProfit: formatPrice(data.total_profit, true),
    totalProfitPercentage: `${data.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${data.contributed_profit_percentage}%` || '0%',
  }
}
export const parseCustomerRow = (data: any) => {
  return {
    id: data.customer_id || '-',
    customerId: data.customer_id || '-',
    customerName: data.name || '-',
    email: data.email || '-',
    customerGroup: data.customer_group_name || '-',
    salesRep: data.rep_name || '-',
    orderCount: data.total_orders || 0,
    totalCost: formatPrice(data.total_cost, true),
    contributedRevenuePercentage: `${data.contributed_revenue_percentage}%` || '0%',
    revenue: formatPrice(data.total_revenue, true) || 0,
    totalProfit: formatPrice(data.total_profit, true) || 0,
    totalProfitPercentage: `${data.profit_percentage}%` || '0%',
    contributedProfitPercentage: `${data.contributed_profit_percentage}%` || '0%',
  }
}

export const parseOrderReportData = (data: any) => {
  return data.map((row: any) => ({
    id: row.id,
    orderDate: row.order_date || '-',
    orderId: row.order_id || '-',
    customerName: row.name || '-',
    customerId: row.customer_id || '-',
    customerEmail: row.email || '-',
    customerGroupName: row.customer_group_name || '-',
    salesRepName: row.sales_rep_name || '-',
    attribution: row.attribution || '-',
    subtotal: row.subtotal || 0,
    couponDiscounts: row.coupon_discounts || 0,
    tax: row.tax || 0,
    shipping: row.shipping || 0,
    total: row.total || 0,
    totalSale: row.total_sell || 0,
    cost: row.total_cost || 0,
    profit: row.total_profit || 0,
    profitPercentage: row.profit_percentage || 0,
    contributedProfitPercentage: row.contributed_profit_percentage || 0,
  }))
}
