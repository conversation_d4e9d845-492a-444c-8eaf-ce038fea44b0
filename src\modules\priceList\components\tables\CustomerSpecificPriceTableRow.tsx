import {useContext, useState, useEffect} from 'react'
import {CustomerSpecificPriceContext} from '../../context'
import Date from '../../../../components/Date/Date'
import {Link} from 'react-router-dom'
import Initials from '../../../../components/Initials'
import usePermission from '../../../../hook/usePermission'
import {useAppSelector} from '../../../../redux/useTypedSelector'

const CustomerSpecificPriceTableRow = ({row, actionComponent, checkBoxComponent}: any) => {
  const {user} = useAppSelector((state) => state.auth)
  const {onUpdateCustomerSpecificPrice} = useContext(CustomerSpecificPriceContext)
  const {hasPermission} = usePermission()
  const isEditable = !!actionComponent && hasPermission('products_price list', 'write')

  // Local state for editing price
  const [price, setPrice] = useState(row.price)
  const [margin, setMargin] = useState(row.margin)
  const [originalPrice, setOriginalPrice] = useState(row.price)
  const [isEditing, setIsEditing] = useState(false)

  // Sync local state if row.price changes externally
  useEffect(() => {
    setPrice(row.price)
    setOriginalPrice(row.price)
    setMargin(row.margin.toFixed(2))
  }, [row.price, row.margin])

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.trim()

    if (value === '') {
      setPrice('')
      setMargin(0)
      return
    }

    // Validate input (only numbers, max 2 decimals)
    if (!/^\d*\.?\d{0,2}$/.test(value)) return

    const numericPrice = parseFloat(value)

    if (numericPrice < 0) {
      setPrice('')
    } else {
      setPrice(value)

      // Recalculate margin
      const cost = parseFloat(row.cost)
      if (cost) {
        const calculatedMargin =
          numericPrice === 0 ? 0 : Number((((numericPrice - cost) / cost) * 100).toFixed(2))
        setMargin(calculatedMargin)
      }
    }
  }

  const savePriceChange = () => {
    setIsEditing(false)

    // Reset if empty
    if (price === '') {
      setPrice(originalPrice)
      return
    }

    // Call API only if price has changed
    const formattedPrice = parseFloat(price)
    if (formattedPrice !== parseFloat(originalPrice)) {
      onUpdateCustomerSpecificPrice(row.product_id, formattedPrice, row.customer_id, row.id)
      setOriginalPrice(price) // Update the original price after saving
    }
  }

  return (
    <tr>
      {checkBoxComponent && <td>{checkBoxComponent(row)}</td>}
      <td>{row.product_id}</td>
      <td>{row.product_name || '-'}</td>
      <td>{row.sku || '-'}</td>
      {user?.role_id !== '67f5f9c43c97938e59357472' &&
        user?.role_id !== '67fd12676af694b36923ce09' &&
        user?.role_id !== '67f92d2959074bcfeb4ff75a' && <td>{row.cost ? `$${row.cost}` : '-'}</td>}
      <td className='text-center'>
        {isEditing && isEditable ? (
          <>
            <input
              type='text'
              className='form-control w-75px text-center d-inline-block'
              value={price}
              onChange={handlePriceChange}
              onBlur={savePriceChange}
              autoFocus
            />
            {
              <div className={`text-nowrap mt-2 ${margin < 10 ? 'text-danger' : ''}`}>
                <span>
                  <strong>M:</strong>
                  <span>{margin}%</span>
                </span>
              </div>
            }
          </>
        ) : (
          <>
            <span
              className={`form-control w-75px text-center d-inline-block cursor-pointer ${
                actionComponent ? '' : 'bg-light'
              }`}
              onClick={() => setIsEditing(true)}
            >
              {price}
            </span>
            {
              <div className={`text-nowrap mt-2 ${margin < 10 ? 'text-danger' : ''}`}>
                <span>
                  <strong>M:</strong>
                  <span>{margin}%</span>
                </span>
              </div>
            }
          </>
        )}
      </td>
      <td>{row.distributor ? `$${row.distributor}` : ''}</td>
      <td>{row.vip ? `$${row.vip}` : ''}</td>
      <td>{row.customer_id}</td>
      <td>
        {row?.name?.trim() ? (
          <Link
            to={`/customers/details/${row.customer_id}`}
            state={{id: row.customer_id}}
            target='_blank'
            className='d-flex align-items-center flex-grow-1 text-start cursor-pointer'
          >
            <div className='symbol symbol-circle symbol-35px me-3'>
              <Initials text={row?.name?.[0]} />
            </div>
            <div className='d-flex flex-column'>
              <div className='align-items-center text-dark fw-semibold'>{row.name}</div>
              <div className='align-items-center text-muted'>{row.email}</div>
            </div>
          </Link>
        ) : (
          '-'
        )}
      </td>
      <td>{row.customer_group_name || '-'}</td>
      <td>{row.price_list || '-'}</td>
      <td>{row.company || '-'}</td>
      <td>{row.rep_name || '-'}</td>
      <td>{row.updated_by || '-'}</td>
      <td>
        <Date date={row.updated_at} />
      </td>
      {actionComponent && <td>{actionComponent(row)}</td>}
    </tr>
  )
}

export default CustomerSpecificPriceTableRow
