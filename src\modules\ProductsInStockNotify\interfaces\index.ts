export interface IInStockNotifyProduct {
  productName: string
  productSku: string
  inquiryCount: number
  hasChildren: boolean
  variants?: IInStockNotifyProduct[]
  productId: string | null
  variantId?: string | null
  variantName?: string
  variantSku?: string | null
  lastCreatedAt?: string
}

export interface IInquiryDetail {
  id: string
  customerName: string
  email: string
  inquiryDate: string
  sku?: string
  productName?: string
  customerGroup?: string
  salesRep?: string
  notifyEmail?: string
  variantId?: string
}

export interface IInquiryDetailsModalProps {
  onClose: () => void
  row: IInStockNotifyProduct
  isParentRow: boolean
}

export interface IInStockNotifyApiResponse {
  data: IInStockNotifyProduct[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
  }
}

export interface IInquiryDetailsApiResponse {
  data: IInquiryDetail[]
}