import {isEmptyObject} from 'jquery'

export function dataToInquiriesPage(data: any) {
  const inquiries: any = []

  // Return an empty array if data is falsy
  if (!data) {
    return []
  }

  // Iterate through the data and push the original keys into inquiries
  data.forEach((element: any) =>
    inquiries.push({
      id: element['id'],
      customer_id: element['customer_id'],
      customer_name: element['customer_name'],
      customer_group_name: element['customer_group_name'],
      customer_email: element['customer_email'],
      product_id: element['product_id'],
      date_submitted: element['date_submitted'],
      message: element['message'],
      status: element['status'],
      updated_by: element['updated_by'],
      updated_at: element['updated_at'],
      rep_email: element['rep_email'],
      rep_name: element['rep_name'],
      product_name: element['product_name'],
      sku: element['sku'] || '-',
      purchaser: element['purchaser_name'] || '-',
      guest: element['is_guest'] || '-',
    })
  )

  return inquiries
}

export const defaultColumns = [
  {date_submitted: 'DATE SUBMITTED'},
  {product_name: 'PRODUCT NAME', default: true},
  {sku: 'SKU', default: true},
  {purchaser: 'PURCHASER', default: false},
  {message: 'MESSAGE', default: true},
  {customer_name: 'CUSTOMER'},
  {customer_group_name: 'CUSTOMER GROUP'},
  {rep_name: 'CUSTOMER REP'},
  {status: 'STATUS'},
  {action: 'ACTION', default: true},
]

export const dataToSortableColumns = (columns: any[], Overlay: any) => {
  const sortableColumns = columns.map((column: any) => {
    const [key, label] = Object.entries(column)[0]

    if (key === 'action') {
      return {
        key,
        label,
        headerStyle: 'min-w-80px',
        render: (row: any) => Overlay(row),
      }
    }

    // Set default headerStyle based on key or any other rule
    let headerStyle = 'min-w-175px'
    let isSorted = false

    if (key === 'product_name') {
      headerStyle = 'min-w-400px'
      isSorted = true
    } else if (key === 'sku') {
      headerStyle = 'min-w-150px w-150px'
      isSorted = true
    } else if (key === 'purchaser') {
      headerStyle = 'min-w-200px w-200px'
      isSorted = false
    } else if (key === 'message') {
      headerStyle = 'min-w-300px'
    } else if (key === 'status') {
      headerStyle = 'min-w-80px'
      isSorted = true
    } else if (key === 'date_submitted') {
      headerStyle = 'min-w-150px'
      isSorted = true
    } else if (key === 'customer_name') {
      headerStyle = 'min-w-250px'
      isSorted = true
    } else if (key === 'customer_group_name') {
      headerStyle = 'min-w-250px'
      isSorted = true
    } else if (key === 'rep_name') {
      headerStyle = 'min-w-200px'
      isSorted = true
    }

    return {
      key,
      label,
      headerStyle,
      isSorted,
      render: (row: any) => Overlay(row),
    }
  })

  return sortableColumns
}

export const formatToLabelValue = (data: any) => {
  if (isEmptyObject(data)) return []
  return Object.entries(data)?.map(([email, name]) => ({
    label: name,
    value: email,
  }))
}

export const removeColumn = (data: any[], columnKey: string) => {
  return data.filter((item: any) => !item.hasOwnProperty(columnKey))
}

export const trimMessage = (message: any) => {
  if (message.length > 150) {
    return message.substring(0, 150) + '...'
  }
  return message
}

export const parseSalesRepReportData = (data: any[]) => {
  return data?.map((item: any) => ({
    salesRep: item.rep_name || '-',
    salesRepEmail: item.rep_email || '-',
    total: item.total || 0,
    open: item.open || 0,
    inProgress: item.in_progress || 0,
    pending: item.pending || 0,
    done: item.done || 0,
    archived: item.archived || 0,
  }))
}

// Table columns definition for SalesRepReport
export const SALES_REP_REPORT_COLUMNS = [
  {
    key: 'rep_email',
    label: 'Sales Rep',
    headerStyle: 'min-w-250px',
    isSorted: true,
    isSearchable: true,
  },
  {
    key: 'total',
    label: 'Total Inquiry',
    headerStyle: 'min-w-150px bg-light',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'open',
    label: 'Open',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'in_progress',
    label: 'In Progress',
    headerStyle: 'min-w-150px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'pending',
    label: 'Pending',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'done',
    label: 'Done',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'archived',
    label: 'Archived',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
]

export const parseProductReportData = (data: any[]) => {
  return data?.map((item: any) => ({
    productName: item.product_name || '-',
    sku: item.sku || '-',
    total: item.total || 0,
    open: item.open || 0,
    inProgress: item.in_progress || 0,
    pending: item.pending || 0,
    done: item.done || 0,
    archived: item.archived || 0,
  }))
}

// Table columns definition for Product Report
export const PRODUCT_REPORT_COLUMNS = [
  {
    key: 'product_name',
    label: 'Product Name',
    headerStyle: 'min-w-400px',
    isSorted: true,
    isSearchable: true,
  },
  {
    key: 'sku',
    label: 'SKU',
    headerStyle: 'min-w-150px',
    isSorted: true,
    isSearchable: true,
  },
  {
    key: 'total',
    label: 'Total Inquiry',
    headerStyle: 'min-w-150px bg-light',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'open',
    label: 'Open',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'in_progress',
    label: 'In Progress',
    headerStyle: 'min-w-150px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'pending',
    label: 'Pending',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'done',
    label: 'Done',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'archived',
    label: 'Archived',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
]

export const parsePurchaserReportData = (data: any[]) => {
  return data?.map((item: any) => ({
    purchaserName: item.purchaser_name || '-',
    purchaserEmail: item.purchaser_email || '-',
    total: item.total || 0,
    open: item.open || 0,
    inProgress: item.in_progress || 0,
    pending: item.pending || 0,
    done: item.done || 0,
    archived: item.archived || 0,
  }))
}

// Table columns definition for Purchaser Report
export const PURCHASER_REPORT_COLUMNS = [
  {
    key: 'purchaser_name',
    label: 'Purchaser Name',
    headerStyle: 'min-w-200px',
    isSorted: true,
    isSearchable: true,
  },
  {
    key: 'total',
    label: 'Total Inquiry',
    headerStyle: 'min-w-150px bg-light',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'open',
    label: 'Open',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'in_progress',
    label: 'In Progress',
    headerStyle: 'min-w-150px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'pending',
    label: 'Pending',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'done',
    label: 'Done',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
  {
    key: 'archived',
    label: 'Archived',
    headerStyle: 'min-w-120px',
    isSorted: true,
    isColumnTotal: true,
    style: 'justify-content-center',
  },
]

export function hasAccess(Representatives: any, user: any) {
  const unrestrictedUsers = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ]

  const emailIds = Representatives?.map((item: any) => item.value)?.filter(
    (email: any) => email !== null
  )

  const canViewAllTickets =
    unrestrictedUsers.includes(user.username) || !emailIds?.includes(user.username)

  if (user.role === 'Customer Representative' && !canViewAllTickets) {
    return {
      canViewAllTickets: false,
      isFilterReadOnly: true,
      repEmail: user.username,
    }
  }

  if (canViewAllTickets) {
    return {
      canViewAllTickets: true,
      isFilterReadOnly: false,
      repEmail: '',
    }
  }

  return {
    canViewAllTickets: false,
    isFilterReadOnly: false,
    repEmail: emailIds?.includes(user.username) ? user.username : '',
  }
}
