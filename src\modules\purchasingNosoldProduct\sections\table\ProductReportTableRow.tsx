import {useEffect, useState} from 'react'
import ProductReportChildTable from './ProductReportChildTable'

const ProductReportTableRow = ({row}: any) => {
  const [showChildTable, setShowChildTable] = useState(false)

  useEffect(() => {
    setShowChildTable(false)
  }, [row])

  return (
    <>
      <tr>
        <td>
          {row?.variants && row?.variants?.length > 0 && (
            <span onClick={() => setShowChildTable(!showChildTable)}>
              {showChildTable ? (
                <i className='bi bi-dash-circle fs-3 fw-semibold cursor-pointer'></i>
              ) : (
                <i className='bi bi-plus-circle fs-3 fw-semibold cursor-pointer'></i>
              )}
            </span>
          )}
        </td>
        <td>{row.sku}</td>
        <td>{row.productName}</td>
        <td>{row.cost}</td>
        <td>{row.purchaserName}</td>
      </tr>
      {showChildTable && row.variants && row.variants.length > 0 && (
        <tr>
          <td colSpan={5}>
            <ProductReportChildTable variants={row.variants} />
          </td>
        </tr>
      )}
    </>
  )
}

export default ProductReportTableRow
