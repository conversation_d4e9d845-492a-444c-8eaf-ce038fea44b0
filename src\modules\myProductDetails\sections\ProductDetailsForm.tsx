import '../../../_metronic/assets/sass/layout/product-details.scss'
import FormIndex from './FormIndex'
import {useForm} from 'react-hook-form'
import React, {useEffect, useRef, useState} from 'react'
import SectionForm from '../../../components/SectionForm/SectionForm'
import {convertKeysToSnakeCase, isEmpty} from '../../../utils/common'
import useProductOperations from '../hooks/useProductOperations'
import {useParams} from 'react-router-dom'
import FormHeader from './Header/FormHeader'
import {
  CUSTOM_FIELDS_CONFIG,
  generateProductVariants,
  setDefaultValues,
  PRODUCT_OPTIONS_CONFIG,
  PRODUCT_VARIANTS_CONFIG,
  SIDEBAR_SECTIONS_DATA,
  setValues,
  handleErrorsAndValidations,
  isError,
} from '../utils'
import {useGetBrandsDropdownList} from '../hooks/useGetBrandsDropdownList'
import {useGetCategoriesDropdownList} from '../hooks/useGetCategoriesDropdownList'
import {useGetProductDetails} from '../hooks/useGetProductDetails'
import Loading from '../../loading'
import {yupResolver} from '@hookform/resolvers/yup'
import {validationSchema} from '../validations'
import {useGetTaxClasses} from '../hooks/useGetTaxClasses'
import ImageTable from './ImageTable'

const ProductDetailsForm = () => {
  const {productDetails, isLoading: isDetailsLoading}: any = useGetProductDetails()
  const {categoriesList, isLoading: isLoadingCategoriesList} = useGetCategoriesDropdownList()
  const {createProduct, updateProduct, onUploadImages, isLoading} = useProductOperations()
  const {brandsList} = useGetBrandsDropdownList()
  const {taxClasses} = useGetTaxClasses()
  const {id: productId, mode} = useParams()
  const descriptionErrorRef = useRef<HTMLDivElement>(null)
  const combinationErrorRef = useRef<HTMLDivElement>(null)
  const publishErrorRef = useRef<HTMLDivElement>(null)
  const [customFieldErrors, setCustomFieldErrors] = useState({
    productOptionErrors: [],
    productVariantErrors: [],
    customFieldErrors: [],
  })
  const [clickedBtn, setClickedBtn] = useState(null)
  const [productStatus, setProductStatus] = useState('draft')
  const [combinations, setCombinations] = useState<any>([])
  const [isReadyToPublish, setIsReadyToPublish] = useState(false)
  const [isDefaultValueSet, setIsDefaultValueSet] = useState(false)
  const [productType, setProductType] = useState(null)

  const submitBtnRefs = {
    draft: useRef<HTMLButtonElement>(null),
    publish: useRef<HTMLButtonElement>(null),
  }

  const {
    control,
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: {errors},
    setError,
  }: any = useForm<any>({
    resolver: yupResolver(validationSchema),
    defaultValues: setDefaultValues(productDetails),
    mode: 'onChange',
  })

  console.log('07-23 watch: ', watch('variants'));
  const onSubmit = (data: any) => {
    if (isError(customFieldErrors, combinations, combinationErrorRef)) return

    const payload = {
      ...convertKeysToSnakeCase(data),
      is_draft: clickedBtn === 'draft',
      ready_to_sync: isReadyToPublish,
      status: clickedBtn === 'draft' ? productStatus : null,
    }

    const action = mode === 'create' ? createProduct : updateProduct
    action(payload, setError)
  }

  useEffect(() => {
    setValues(
      productId,
      isDetailsLoading,
      isLoadingCategoriesList,
      isDefaultValueSet,
      reset,
      productDetails,
      setIsDefaultValueSet,
      setProductStatus
    )
    // eslint-disable-next-line
  }, [productId, isDetailsLoading, isLoadingCategoriesList, isDefaultValueSet, productDetails])

  useEffect(() => {
    handleErrorsAndValidations(
      watch,
      setIsReadyToPublish,
      setProductType,
      errors,
      descriptionErrorRef,
      productType,
      setValue,
      productDetails?.error,
      publishErrorRef
    )
    // eslint-disable-next-line
  }, [watch, errors, productType, productDetails?.error])

  return (
    <form
      id='kt_modal_add_user_form'
      className='form product-detail-page-wrapper position-relative product-create-wrapper clearfix'
      onSubmit={handleSubmit(onSubmit)}
    >
      {(isDetailsLoading || isLoadingCategoriesList || isLoading) && <Loading />}
      <FormIndex sidebarSections={SIDEBAR_SECTIONS_DATA} productType={productType} />
      <div className='product-page-content page-content-no-overflow'>
        <div className='pe-10 ps-10 pt-0'>
          {productDetails?.status !== 'published' &&
            productDetails?.status !== 'publishing' &&
            mode !== 'view' &&
            !isDetailsLoading &&
            !isLoadingCategoriesList && (
              <FormHeader
                submitBtnRefs={submitBtnRefs}
                isLoading={isLoading}
                clickedBtn={clickedBtn}
                setClickedBtn={setClickedBtn}
                isReadyToPublish={isReadyToPublish}
                productStatus={productStatus}
                setProductStatus={setProductStatus}
              />
            )}

          {productDetails && !isEmpty(productDetails.error) && (
            <div className='alert alert-danger mb-10' ref={publishErrorRef}>
              {typeof productDetails.error === 'string'
                ? productDetails.error
                : typeof productDetails.error === 'object'
                ? Object.keys(productDetails.error).map((key) => (
                    <ul>
                      <li key={key}>
                        <span className='fw-bold'>{key}:</span> {productDetails.error[key]}
                      </li>
                    </ul>
                  ))
                : productDetails.error.map((err: any, index: any) => (
                    <span key={index}>{err}</span>
                  ))}
            </div>
          )}

          <SectionForm
            id='basic-info'
            title='Basic Information'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            control={control}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isCheckBox: true,
                id: 'visible-on-store',
                registerKey: 'isVisible',
                label: 'Visible on Store',
                class: 'form-check-solid col-md-12 mb-10 ',
              },
              {
                id: 'product-name',
                registerKey: 'name',
                label: 'Product Name',
                maxLength: 255,
                class: 'col-md-12 mb-7',
                required: true,
              },
              {
                id: 'sku',
                registerKey: 'sku',
                label: 'SKU',
                class: 'col-md-6 mb-7',
                required: true,
                maxLength: 255,
              },
              {
                isSelect: true,
                id: 'product-type',
                registerKey: 'type',
                label: 'Product Type',
                class: 'col-md-6 mb-7',
                options: [
                  {label: 'Physical', value: 'physical'},
                  {label: 'Digital', value: 'digital'},
                ],
              },
              {
                isSelect: true,
                id: 'brand',
                registerKey: 'brandId',
                label: 'Brand',
                class: 'col-md-6 mb-7',
                options: brandsList,
                labelKey: 'name',
                valueKey: 'id',
              },
              {
                isMultiSelectTree: true,
                isSearch: true,
                id: 'categories',
                registerKey: 'categories',
                label: 'Category',
                class: 'col-md-6 mb-7',
                modalBodyClass: 'mh-500px',
                required: true,
                options: categoriesList,
                isLoading: isLoadingCategoriesList || isDetailsLoading,
                component: (data: any) =>
                  React.Children.toArray([<td className='w-100 min-h-100'>{data.name}</td>]),
                labelKey: 'name',
                valueKey: 'id',
                returnKeys: ['id', 'name'],
                isDisableExpand: true,
                isDefaultExpandAll: true,
              },
            ]}
          />

          <SectionForm
            id='pricing'
            title='Pricing'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isPriceInput: true,
                isFloat: true,
                id: 'default-price',
                registerKey: 'price',
                label: 'Default Price (including tax)',
                class: 'col-md-6 mb-7',
                required: true,
              },
              {
                isPriceInput: true,
                id: 'cost',
                isFloat: true,
                registerKey: 'costPrice',
                label: 'Cost',
                class: 'col-md-6 mb-7',
              },
              {
                isPriceInput: true,
                id: 'msrp',
                isFloat: true,
                registerKey: 'retailPrice',
                label: 'MSRP',
                class: 'col-md-6 mb-7',
              },
              {
                isPriceInput: true,
                id: 'sale-price',
                isFloat: true,
                registerKey: 'salePrice',
                label: 'Sale Price',
                class: 'col-md-6 mb-7',
              },
              {
                isSelect: true,
                id: 'tax-class',
                registerKey: 'taxClassId',
                label: 'Tax Class',
                defaultValues: '0',
                class: 'col-md-6 mb-7',
                options: taxClasses?.map((taxClass: any) => ({
                  label: taxClass?.name,
                  value: taxClass?.id,
                })),
              },
            ]}
          />

          <SectionForm
            id='description'
            title='Description'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            errors={errors}
            control={control}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isTextEditor: true,
                id: 'description',
                registerKey: 'description',
                errorClass: 'alert alert-danger mt-4',
                maxLength: 65642,
                errorMessageRef: descriptionErrorRef,
              },
            ]}
          />

          <SectionForm
            id='images'
            title='Images'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            setValue={setValue}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isDropZone: true,
                id: 'images',
                registerKey: 'images',
                isReplaceable: true,
                defaultValues: productDetails?.images,
                component: ImageTable,
                apiFunction: onUploadImages,
                componentProps: {productStatus: productDetails?.status, mode: mode},
                maxFileUploads: 1000,
                totalFileSize: 8,
              },
            ]}
          />

          <SectionForm
            id='product-identifier'
            title='Product Identifiers'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                id: 'manufacturer-part-number',
                registerKey: 'mpn',
                label: 'Manufacturer Part Number (MPN)',
                class: 'col-md-6 mb-7',
                maxLength: 20,
              },
              {
                isNumberInput: true,
                id: 'product-upc-ean',
                registerKey: 'upc',
                label: 'Product UPC/EAN',
                class: 'col-md-6 mb-7',
                maxLength: 14,
              },
              {
                isNumberInput: true,
                id: 'global-trade-item-number',
                registerKey: 'gtin',
                label: 'Global Trade Item Number (GTIN)',
                class: 'col-md-6 mb-7',
                maxLength: 14,
              },
              {
                id: 'bin-picking-number',
                registerKey: 'binPickingNumber',
                label: 'Bin Picking Number (BPN)',
                class: 'col-md-6 mb-7',
                maxLength: 20,
              },
            ]}
          />

          <SectionForm
            id='product-options'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            submitBtnRef={submitBtnRefs}
            control={control}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isCustomFields: true,
                id: 'product-options',
                registerKey: 'options',
                config: PRODUCT_OPTIONS_CONFIG,
                onChange: (data: any) =>
                  setCombinations(generateProductVariants(data, productDetails?.variants)),
                onError: (errors: any) =>
                  setCustomFieldErrors({...customFieldErrors, productOptionErrors: errors}),
                tableSectionClass: 'w-100',
                tableClass: 'w-100 table-row-dashed',
                isDeletable: true,
              },
            ]}
          >
            {combinations?.length > 600 && (
              <div className='d-flex justify-content-center mt-10' ref={combinationErrorRef}>
                <p className='alert alert-danger col-md-8'>
                  Warning: The number of combinations <strong>{combinations?.length}</strong>{' '}
                  exceeds the allowed limit of 600. Please reduce the number of combinations.
                </p>
              </div>
            )}
          </SectionForm>

          {/* Product Variants Section with SKU Vault Checkbox */}
          <SectionForm
            id='product-variants'
            submitBtnRef={submitBtnRefs}
            control={control}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            className={
              combinations?.length === 0 || combinations?.length > 600
                ? 'd-none'
                : 'product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            }
            inputs={[
              {
                isCustomFields: true,
                id: 'product-variants',
                registerKey: 'variants',
                config: PRODUCT_VARIANTS_CONFIG,
                value: combinations,
                onError: (errors: any) =>
                  setCustomFieldErrors({...customFieldErrors, productVariantErrors: errors}),
                isDisableAddBtn: true,
                isDeletable: false,
              },
            ]}
          />

          <SectionForm
            id='storefront-details'
            title='Storefront Details'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                id: 'search-registerKeywords',
                registerKey: 'searchKeywords',
                label: 'Search Keywords',
                class: 'col-md-6 mb-7',
                maxLength: 250,
              },
              {
                isNumberInput: true,
                id: 'sort-order',
                registerKey: 'sortOrder',
                label: 'Sort Order',
                class: 'col-md-6 mb-7',
                maxLength: 10,
              },
              {
                isTextArea: true,
                id: 'warranty-info',
                registerKey: 'warranty',
                label: 'Warranty Information',
                class: 'col-md-12 mb-7',
                maxLength: 65642,
              },
              {
                id: 'availability-text',
                registerKey: 'availabilityDescription',
                label: 'Availability Text',
                class: 'col-md-4 mb-7',
                maxLength: 100,
              },
              {
                isSelect: true,
                id: 'condition',
                registerKey: 'condition',
                label: 'Condition',
                class: 'col-md-4 mb-7',
                options: [
                  {label: 'Used', value: 'Used'},
                  {label: 'New', value: 'New'},
                  {label: 'Refurbished', value: 'Refurbished'},
                ],
              },
              {
                isCheckBox: true,
                id: 'show-condition-on-storefront',
                registerKey: 'isConditionShown',
                label: 'Show Condition on Storefront',
                class: 'form-check-solid col-md-4 mb-7 mt-10',
              },
            ]}
          />

          <SectionForm
            id='custom-fields'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            control={control}
            submitBtnRef={submitBtnRefs}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                id: 'custom-fields',
                isCustomFields: true,
                registerKey: 'customFields',
                config: CUSTOM_FIELDS_CONFIG,
                onError: (errors: any) =>
                  setCustomFieldErrors({...customFieldErrors, customFieldErrors: errors}),
                tableClass: 'w-100 table-row-dashed',
              },
            ]}
          />

          {productType !== 'digital' && (
            <SectionForm
              id='dimensions-weight'
              title='Dimensions & Weight'
              className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
              register={register}
              errors={errors}
              isDisabled={
                mode === 'view' ||
                productDetails?.status === 'published' ||
                productDetails?.status === 'publishing'
              }
              inputs={[
                {
                  isNumberInput: true,
                  isFloat: true,
                  id: 'weight',
                  registerKey: 'weight',
                  label: 'Weight (KGS)',
                  class: 'col-md-6 mb-7',
                  required: true,
                  maxLength: 30,
                },
                {
                  isNumberInput: true,
                  isFloat: true,
                  id: 'width',
                  registerKey: 'width',
                  label: 'Width (Centimeters)',
                  class: 'col-md-6 mb-7',
                  maxLength: 30,
                },
                {
                  isNumberInput: true,
                  isFloat: true,
                  id: 'height',
                  registerKey: 'height',
                  label: 'Height (Centimeters)',
                  class: 'col-md-6 mb-7',
                  maxLength: 30,
                },
                {
                  isNumberInput: true,
                  id: 'depth',
                  isFloat: true,
                  registerKey: 'depth',
                  label: 'Depth (Centimeters)',
                  class: 'col-md-6 mb-7',
                  maxLength: 30,
                },
              ]}
            />
          )}

          <SectionForm
            id='shipping-details'
            title='Shipping Details'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isPriceInput: true,
                isFloat: true,
                id: 'fixed-shipping-price',
                registerKey: 'fixedCostShippingPrice',
                label: 'Fixed Shipping Price',
                class: 'col-md-6 mb-7',
              },
              {
                isCheckBox: true,
                id: 'free-shipping',
                registerKey: 'isFreeShipping',
                label: 'Free shipping',
                class: 'form-check-solid col-md-6 mb-7 mt-11',
              },
            ]}
          />

          <SectionForm
            id='purchasability'
            title='Purchasability'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isRadio: true,
                id: 'online-store',
                registerKey: 'availability',
                value: 'available',
                label: 'This product can be purchased in my online store',
                class: 'col-md-12 mb-7',
              },
              {
                isRadio: true,
                id: 'pre-order',
                registerKey: 'availability',
                value: 'preorder',
                label: 'This product is coming soon but I want to take pre-orders',
                class: 'col-md-12 mb-7',
              },
              {
                isRadio: true,
                id: 'offline-store',
                registerKey: 'availability',
                value: 'disabled',
                label: 'This product cannot be purchased in my online store',
                class: 'col-md-12 mb-15',
              },
              {
                isNumberInput: true,
                id: 'min-purchase-quantity',
                registerKey: 'orderQuantityMinimum',
                label: 'Minimum Purchase Quantity',
                class: 'col-md-6 mb-7',
                maxLength: 10,
              },
              {
                isNumberInput: true,
                id: 'max-purchase-quantity',
                registerKey: 'orderQuantityMaximum',
                label: 'Maximum Purchase Quantity',
                class: 'col-md-6 mb-7',
                maxLength: 10,
              },
            ]}
          />

          <SectionForm
            id='seo'
            title='Search Engine Optimization'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                id: 'page-title',
                registerKey: 'pageTitle',
                label: 'Page Title',
                class: 'col-md-12 mb-7',
                maxLength: 255,
              },
              {
                id: 'meta-description',
                registerKey: 'metaDescription',
                label: 'Meta Description',
                class: 'col-md-12 mb-7',
                maxLength: 255,
              },
            ]}
          />

          {/* SKU VAULT Product Info Section */}
          <SectionForm
            id='sku-vault-product-info'
            title='SKU Vault Product Info'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
            register={register}
            control={control}
            errors={errors}
            isDisabled={
              mode === 'view' ||
              productDetails?.status === 'published' ||
              productDetails?.status === 'publishing'
            }
            inputs={[
              {
                isSelect: true,
                id: 'skuvault-brand',
                registerKey: 'skuvault.brand',
                label: 'Brand',
                class: 'col-md-6 mb-7',
                required: true,
                options: brandsList,
                labelKey: 'name',
                valueKey: 'id',
              },
              {
                isSelect: true,
                id: 'skuvault-classification',
                registerKey: 'skuvault.classification',
                label: 'Classification',
                class: 'col-md-6 mb-7',
                required: true,
                options: [], // TODO: Provide classification options
              },
              {
                isMultiSelect: true,
                id: 'skuvault-suppliers',
                registerKey: 'skuvault.supplier_info',
                label: 'Suppliers',
                class: 'col-md-6 mb-7',
                required: true,
                options: [], // TODO: Provide suppliers options
              },
              {
                isMultiSelect: true,
                id: 'skuvault-statuses',
                registerKey: 'skuvault.statuses',
                label: 'Status',
                class: 'col-md-6 mb-7',
                options: [], // TODO: Provide status options
              },
              {
                isNumberInput: true,
                id: 'skuvault-incremental-quantity',
                registerKey: 'skuvault.incremental_quantity',
                label: 'Incremental Quantity',
                class: 'col-md-6 mb-7',
              },
            ]}
          />

          {/* SKU VAULT Attributes Section (static inputs) */}
          <div
            id='sku-vault-attributes'
            className='product-section mb-10 border-gray-300 border-2 border-bottom-dotted pb-10'
          >
            <h3 className='mb-7'>SKU Vault Attributes</h3>
            <div className='row'>
              {[
                {Name: 'Highlight', Value: ''},
                {Name: 'Pack', Value: ''},
                {Name: 'Bottles/Pods Quantity', Value: ''},
                {Name: 'Case Qty', Value: ''},
                {Name: 'ML of Nicotine', Value: ''},
                {Name: 'Box Qty', Value: ''},
                {Name: 'Box Height', Value: ''},
                {Name: 'Box Length', Value: ''},
                {Name: 'Box Weight', Value: ''},
                {Name: 'Box Width', Value: ''},
                {Name: 'Box Dim Weight', Value: ''},
                {Name: 'Box Volume', Value: ''},
                {Name: 'Case Height', Value: ''},
                {Name: 'Case Length', Value: ''},
                {Name: 'Case Weight', Value: ''},
                {Name: 'Case Width', Value: ''},
                {Name: 'Case Dim Weight', Value: ''},
                {Name: 'Sold Type', Value: ''},
                {Name: 'Case Volume', Value: ''},
                {Name: 'BOX UPC', Value: ''},
                {Name: 'CASE UPC', Value: ''},
                {Name: 'Product Height', Value: ''},
                {Name: 'Product Length', Value: ''},
                {Name: 'Product Width', Value: ''},
              ].map((attr, idx) => (
                <div className='col-md-6 mb-7' key={attr.Name}>
                  <label className='form-label'>{attr.Name}</label>
                  <input
                    type='text'
                    className='form-control'
                    {...register(`skuvault.attributes.${idx}.Value`)}
                    defaultValue={attr.Value}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </form>
  )
}

export default ProductDetailsForm
