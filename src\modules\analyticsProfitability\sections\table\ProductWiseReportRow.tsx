import {Link} from 'react-router-dom'

const ProductWiseReportRow = ({row}: any) => {
  return (
    <tr>
      <td>{row.productName}</td>
      <td>{row.sku}</td>
      <td>{row.classifications?.map((item: any) => item).join(', ')}</td>
      <td>{row.suppliers?.map((item: any) => item).join(', ')}</td>
      {/* <td>{row.averageCostPerPiece}</td>
      <td>{row.averageSellingPricePerPiece}</td> */}
      <td>
        <Link
          to={{
            pathname: '/analytics/profitability/profitability-report',
            search: `?view=order-report&product_id=${row.productId}`,
          }}
          target='_blank'
          className='text-dark text-decoration-underline'
        >
          {row.orderCount}
        </Link>
      </td>
      <td>{row.totalCost}</td>
      <td>{row.totalSale}</td>
      <td>{row.totalProfit}</td>
      <td>{row.totalProfitPercentage}</td>
      <td className='text-end'>{row.contributedProfitPercentage}</td>
    </tr>
  )
}

export default ProductWiseReportRow
